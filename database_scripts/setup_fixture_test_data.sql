-- Script to set up test data for competition fixtures functionality
-- This script creates test data for teams that have advanced to the next phase

USE [GoldRushThunee]
GO

-- First, let's check if we have a test competition
DECLARE @CompetitionId UNIQUEIDENTIFIER = '4860c19d-e3f3-4e2d-b359-275527461bd6'

-- Update some teams to have AdvancedToNextPhase = 1
UPDATE CompetitionTeams 
SET AdvancedToNextPhase = 1,
    Phase = 'Top16'
WHERE CompetitionId = @CompetitionId 
  AND Points >= 15  -- Teams with good performance
  AND IsEliminated = 0

-- Create some phase lobbies for the Top16 phase
DECLARE @LobbyId1 UNIQUEIDENTIFIER = NEWID()
DECLARE @LobbyId2 UNIQUEIDENTIFIER = NEWID()
DECLARE @LobbyId3 UNIQUEIDENTIFIER = NEWID()

-- Get some advanced teams for testing
DECLARE @Team1Id UNIQUEIDENTIFIER, @Team2Id UNIQUEIDENTIFIER, @Team3Id UNIQUEIDENTIFIER, @Team4Id UNIQUEIDENTIFIER

SELECT TOP 4 
    @Team1Id = CASE WHEN ROW_NUMBER() OVER (ORDER BY Points DESC) = 1 THEN Id ELSE @Team1Id END,
    @Team2Id = CASE WHEN ROW_NUMBER() OVER (ORDER BY Points DESC) = 2 THEN Id ELSE @Team2Id END,
    @Team3Id = CASE WHEN ROW_NUMBER() OVER (ORDER BY Points DESC) = 3 THEN Id ELSE @Team3Id END,
    @Team4Id = CASE WHEN ROW_NUMBER() OVER (ORDER BY Points DESC) = 4 THEN Id ELSE @Team4Id END
FROM CompetitionTeams 
WHERE CompetitionId = @CompetitionId 
  AND AdvancedToNextPhase = 1
ORDER BY Points DESC

-- Create phase lobbies
INSERT INTO CompetitionPhaseLobbies (Id, CompetitionId, Phase, LobbyCode, CreatedByAdminId, CreatedAt, MatchScheduledAt, BestOfGames, RequiredWins, MatchStatus)
VALUES 
    (@LobbyId1, @CompetitionId, 'Top16', 'T16M001', '00000000-0000-0000-0000-000000000001', GETUTCDATE(), DATEADD(HOUR, 2, GETUTCDATE()), 3, 2, 'Pending'),
    (@LobbyId2, @CompetitionId, 'Top16', 'T16M002', '00000000-0000-0000-0000-000000000001', GETUTCDATE(), DATEADD(HOUR, 4, GETUTCDATE()), 3, 2, 'Pending'),
    (@LobbyId3, @CompetitionId, 'Top16', 'T16M003', '00000000-0000-0000-0000-000000000001', GETUTCDATE(), DATEADD(DAY, 1, GETUTCDATE()), 3, 2, 'InProgress')

-- Add teams to lobbies
IF @Team1Id IS NOT NULL AND @Team2Id IS NOT NULL
BEGIN
    INSERT INTO CompetitionPhaseLobbyTeams (Id, LobbyId, CompetitionTeamId, IsWinner, EliminatedAt)
    VALUES 
        (NEWID(), @LobbyId1, @Team1Id, 0, NULL),
        (NEWID(), @LobbyId1, @Team2Id, 0, NULL)
END

IF @Team3Id IS NOT NULL AND @Team4Id IS NOT NULL
BEGIN
    INSERT INTO CompetitionPhaseLobbyTeams (Id, LobbyId, CompetitionTeamId, IsWinner, EliminatedAt)
    VALUES 
        (NEWID(), @LobbyId2, @Team3Id, 0, NULL),
        (NEWID(), @LobbyId2, @Team4Id, 0, NULL)
END

-- Create a completed match example
DECLARE @LobbyId4 UNIQUEIDENTIFIER = NEWID()
DECLARE @Team5Id UNIQUEIDENTIFIER, @Team6Id UNIQUEIDENTIFIER

SELECT TOP 2 
    @Team5Id = CASE WHEN ROW_NUMBER() OVER (ORDER BY Points DESC) = 5 THEN Id ELSE @Team5Id END,
    @Team6Id = CASE WHEN ROW_NUMBER() OVER (ORDER BY Points DESC) = 6 THEN Id ELSE @Team6Id END
FROM CompetitionTeams 
WHERE CompetitionId = @CompetitionId 
  AND AdvancedToNextPhase = 1
ORDER BY Points DESC
OFFSET 4 ROWS

IF @Team5Id IS NOT NULL AND @Team6Id IS NOT NULL
BEGIN
    INSERT INTO CompetitionPhaseLobbies (Id, CompetitionId, Phase, LobbyCode, CreatedByAdminId, CreatedAt, MatchScheduledAt, BestOfGames, RequiredWins, MatchStatus, CompletedAt)
    VALUES (@LobbyId4, @CompetitionId, 'Top16', 'T16M004', '00000000-0000-0000-0000-000000000001', DATEADD(DAY, -1, GETUTCDATE()), DATEADD(DAY, -1, GETUTCDATE()), 3, 2, 'Completed', GETUTCDATE())

    INSERT INTO CompetitionPhaseLobbyTeams (Id, LobbyId, CompetitionTeamId, IsWinner, EliminatedAt)
    VALUES 
        (NEWID(), @LobbyId4, @Team5Id, 1, NULL),  -- Winner
        (NEWID(), @LobbyId4, @Team6Id, 0, GETUTCDATE())  -- Loser
END

-- Update the competition phase
UPDATE Competitions 
SET Phase = 'Top16',
    PhaseEndDate = DATEADD(DAY, 3, GETUTCDATE())
WHERE Id = @CompetitionId

-- Display results for verification
SELECT 'Competition Teams with Advanced Status' as ResultType
SELECT 
    ct.Id,
    ct.TeamName,
    ct.Points,
    ct.Phase,
    ct.AdvancedToNextPhase,
    u1.Username as Player1,
    u2.Username as Player2
FROM CompetitionTeams ct
LEFT JOIN Users u1 ON ct.Player1Id = u1.Id
LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
WHERE ct.CompetitionId = @CompetitionId 
  AND ct.AdvancedToNextPhase = 1
ORDER BY ct.Points DESC

SELECT 'Phase Lobbies Created' as ResultType
SELECT 
    cpl.Id,
    cpl.LobbyCode,
    cpl.Phase,
    cpl.MatchScheduledAt,
    cpl.MatchStatus,
    cpl.CompletedAt
FROM CompetitionPhaseLobbies cpl
WHERE cpl.CompetitionId = @CompetitionId
ORDER BY cpl.CreatedAt DESC

SELECT 'Lobby Teams' as ResultType
SELECT 
    cpl.LobbyCode,
    ct.TeamName,
    cplt.IsWinner,
    cplt.EliminatedAt,
    u1.Username as Player1,
    u2.Username as Player2
FROM CompetitionPhaseLobbyTeams cplt
INNER JOIN CompetitionPhaseLobbies cpl ON cplt.LobbyId = cpl.Id
INNER JOIN CompetitionTeams ct ON cplt.CompetitionTeamId = ct.Id
LEFT JOIN Users u1 ON ct.Player1Id = u1.Id
LEFT JOIN Users u2 ON ct.Player2Id = u2.Id
WHERE cpl.CompetitionId = @CompetitionId
ORDER BY cpl.LobbyCode, ct.TeamName

PRINT 'Test data setup completed successfully!'
PRINT 'Teams with AdvancedToNextPhase = 1 should now see fixtures in the frontend.'
