-- =============================================
-- MASTER DATABASE RECREATION SCRIPT
-- =============================================
-- This script runs the complete database recreation in the correct order
-- 
-- INSTRUCTIONS:
-- 1. Connect to SQL Server using SSMS as user 'EG-Dev'
-- 2. Run this script as a single execution
-- 3. This will completely recreate the GoldRushThunee database
-- 4. ALL EXISTING DATA WILL BE LOST
-- =============================================

PRINT '==============================================';
PRINT 'MASTER DATABASE RECREATION SCRIPT';
PRINT 'Starting complete database recreation...';
PRINT 'Server: **************';
PRINT 'Database: GoldRushThunee';
PRINT 'User: EG-Dev';
PRINT 'Timestamp: ' + CONVERT(VARCHAR, GETDATE(), 120);
PRINT '';
PRINT 'WARNING: This will DROP and recreate the entire database!';
PRINT 'ALL EXISTING DATA WILL BE LOST!';
PRINT '==============================================';
PRINT '';

-- Execute Part 1: Database, Tables, Views, Core Procedures
PRINT 'EXECUTING PART 1: Database, Tables, Views, Core Procedures...';
:r "COMPLETE_DATABASE_RECREATION_SCRIPT.sql"

-- Execute Part 2: Additional Stored Procedures
PRINT '';
PRINT 'EXECUTING PART 2: Additional Stored Procedures...';
:r "COMPLETE_DATABASE_RECREATION_SCRIPT_PART2.sql"

PRINT '';
PRINT '==============================================';
PRINT 'COMPLETE DATABASE RECREATION FINISHED!';
PRINT '';
PRINT 'Database Objects Created:';
PRINT '- 8 Tables with all indexes and constraints';
PRINT '- 3 Views for statistics and reporting';
PRINT '- 15+ Stored Procedures for all operations';
PRINT '- Foreign key relationships';
PRINT '- Check constraints for data integrity';
PRINT '';
PRINT 'Database is fully ready for use!';
PRINT '';
PRINT 'Connection String:';
PRINT 'Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;';
PRINT '';
PRINT 'You can now:';
PRINT '1. Test database connectivity';
PRINT '2. Configure your application connection string';
PRINT '3. Start your ThuneeAPI application';
PRINT '4. Create sample data if needed';
PRINT '';
PRINT 'Database recreation completed successfully!';
PRINT '==============================================';
