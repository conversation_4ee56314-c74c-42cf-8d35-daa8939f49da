# =============================================
# PowerShell Script to Recreate GoldRushThunee Database
# =============================================
# This script automates the database recreation process
# 
# REQUIREMENTS:
# - SQL Server Management Studio or sqlcmd installed
# - Access to server ************** with user EG-Dev
# =============================================

param(
    [string]$Server = "**************",
    [string]$Username = "EG-Dev",
    [string]$Password = "Password01?",
    [string]$Database = "GoldRushThunee"
)

Write-Host "=============================================="
Write-Host "GoldRushThunee Database Recreation Script"
Write-Host "=============================================="
Write-Host "Server: $Server"
Write-Host "Username: $Username"
Write-Host "Database: $Database"
Write-Host "Timestamp: $(Get-Date)"
Write-Host ""

# Check if sqlcmd is available
try {
    $sqlcmdVersion = sqlcmd -?
    Write-Host "✓ sqlcmd is available"
} catch {
    Write-Host "❌ ERROR: sqlcmd is not available. Please install SQL Server Command Line Utilities."
    Write-Host "Download from: https://docs.microsoft.com/en-us/sql/tools/sqlcmd-utility"
    exit 1
}

# Get the script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Write-Host "Script directory: $ScriptDir"

# Define script files
$Part1Script = Join-Path $ScriptDir "COMPLETE_DATABASE_RECREATION_SCRIPT.sql"
$Part2Script = Join-Path $ScriptDir "COMPLETE_DATABASE_RECREATION_SCRIPT_PART2.sql"

# Check if script files exist
if (-not (Test-Path $Part1Script)) {
    Write-Host "❌ ERROR: Part 1 script not found: $Part1Script"
    exit 1
}

if (-not (Test-Path $Part2Script)) {
    Write-Host "❌ ERROR: Part 2 script not found: $Part2Script"
    exit 1
}

Write-Host "✓ All script files found"
Write-Host ""

# Confirm with user
Write-Host "WARNING: This will completely recreate the database!"
Write-Host "ALL EXISTING DATA WILL BE LOST!"
Write-Host ""
$confirmation = Read-Host "Are you sure you want to continue? (yes/no)"

if ($confirmation -ne "yes") {
    Write-Host "Operation cancelled by user."
    exit 0
}

Write-Host ""
Write-Host "Starting database recreation..."
Write-Host ""

# Execute Part 1
Write-Host "Executing Part 1: Database, Tables, Views, Core Procedures..."
try {
    $result1 = sqlcmd -S $Server -U $Username -P $Password -i $Part1Script -b
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Part 1 completed successfully"
    } else {
        Write-Host "❌ Part 1 failed with exit code: $LASTEXITCODE"
        Write-Host $result1
        exit 1
    }
} catch {
    Write-Host "❌ ERROR executing Part 1: $($_.Exception.Message)"
    exit 1
}

Write-Host ""

# Execute Part 2
Write-Host "Executing Part 2: Additional Stored Procedures..."
try {
    $result2 = sqlcmd -S $Server -U $Username -P $Password -i $Part2Script -b
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Part 2 completed successfully"
    } else {
        Write-Host "❌ Part 2 failed with exit code: $LASTEXITCODE"
        Write-Host $result2
        exit 1
    }
} catch {
    Write-Host "❌ ERROR executing Part 2: $($_.Exception.Message)"
    exit 1
}

Write-Host ""
Write-Host "=============================================="
Write-Host "DATABASE RECREATION COMPLETED SUCCESSFULLY!"
Write-Host "=============================================="
Write-Host ""
Write-Host "Database Objects Created:"
Write-Host "- 8 Tables with all indexes and constraints"
Write-Host "- 3 Views for statistics and reporting"
Write-Host "- 15+ Stored Procedures for all operations"
Write-Host "- Foreign key relationships"
Write-Host "- Check constraints for data integrity"
Write-Host ""
Write-Host "Connection String:"
Write-Host "Server=$Server; Database=$Database; User Id=$Username; Password=$Password; TrustServerCertificate=True;"
Write-Host ""
Write-Host "Next Steps:"
Write-Host "1. Test database connectivity"
Write-Host "2. Configure your application connection string"
Write-Host "3. Start your ThuneeAPI application"
Write-Host "4. Create sample data if needed"
Write-Host ""
Write-Host "Database is ready for use!"
Write-Host "=============================================="
