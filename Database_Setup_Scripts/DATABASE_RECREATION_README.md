# GoldRushThunee Database Recreation Guide

## Overview
This guide provides complete instructions for recreating the GoldRushThunee database from scratch. Use these scripts when your database has been deleted or corrupted and needs to be completely rebuilt.

## ⚠️ IMPORTANT WARNING
**These scripts will completely DROP and recreate the database. ALL EXISTING DATA WILL BE LOST!**

## Database Information
- **Server**: **************
- **Database**: GoldRushThunee
- **User**: EG-Dev
- **Password**: Password01?

## Quick Start (Recommended)

### Option 1: PowerShell Script (Automated)
1. Open PowerShell as Administrator
2. Navigate to the `Database_Setup_Scripts` folder
3. Run the automated script:
   ```powershell
   .\Run_Database_Recreation.ps1
   ```
4. Confirm when prompted
5. Wait for completion

### Option 2: SQL Server Management Studio (Manual)
1. Open SQL Server Management Studio (SSMS)
2. Connect to server `**************` with user `EG-Dev`
3. Open and execute `COMPLETE_DATABASE_RECREATION_SCRIPT.sql`
4. Then open and execute `COMPLETE_DATABASE_RECREATION_SCRIPT_PART2.sql`

### Option 3: Single Master Script
1. Open SSMS and connect to the server
2. Open and execute `RUN_COMPLETE_DATABASE_RECREATION.sql`
   - This script automatically runs both parts in sequence

## Script Files Description

### Main Recreation Scripts
- **`COMPLETE_DATABASE_RECREATION_SCRIPT.sql`** - Part 1: Database, tables, views, core procedures
- **`COMPLETE_DATABASE_RECREATION_SCRIPT_PART2.sql`** - Part 2: Additional stored procedures
- **`RUN_COMPLETE_DATABASE_RECREATION.sql`** - Master script that runs both parts
- **`Run_Database_Recreation.ps1`** - PowerShell automation script

### What Gets Created

#### Tables (8 total)
1. **Users** - Player accounts and authentication
2. **Competitions** - Tournament definitions and settings
3. **CompetitionTeams** - Team registrations with invite codes
4. **CompetitionTeamInvites** - Partner invitation system
5. **Games** - Game sessions and results
6. **GameBalls** - Individual ball results within games
7. **GameHands** - Individual hand results within balls
8. **PlayedCards** - Card tracking for each hand

#### Views (3 total)
1. **UserStatistics** - Aggregated player performance metrics
2. **CompetitionLeaderboard** - Real-time competition rankings
3. **GameHistory** - Complete game records with player details

#### Stored Procedures (15+ total)
- **User Management**: Create, get, update users
- **Competition Management**: Create, get competitions
- **Game Management**: Create, update, complete games
- **Leaderboard Functions**: Global and competition leaderboards
- **Team Management**: Join teams, accept invites
- **Game Tracking**: Record hands, balls, and card plays

## Step-by-Step Manual Process

### Step 1: Prepare Environment
1. Ensure you have access to SQL Server **************
2. Verify you can connect with user `EG-Dev`
3. Have SQL Server Management Studio installed

### Step 2: Execute Scripts
1. **Run Part 1**:
   ```sql
   -- Execute COMPLETE_DATABASE_RECREATION_SCRIPT.sql
   -- This creates: Database, Tables, Views, Core Procedures
   ```

2. **Run Part 2**:
   ```sql
   -- Execute COMPLETE_DATABASE_RECREATION_SCRIPT_PART2.sql
   -- This creates: Additional Stored Procedures
   ```

### Step 3: Verify Creation
Run these verification queries:
```sql
-- Check tables
SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';

-- Check views
SELECT COUNT(*) as ViewCount FROM INFORMATION_SCHEMA.VIEWS;

-- Check stored procedures
SELECT COUNT(*) as ProcedureCount FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_TYPE = 'PROCEDURE';

-- Test basic functionality
SELECT COUNT(*) FROM Users;
SELECT COUNT(*) FROM Competitions;
```

## Connection String
After successful recreation, use this connection string in your application:
```
Server=**************; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;
```

## Troubleshooting

### Common Issues
1. **Connection Failed**: Verify server access and credentials
2. **Permission Denied**: Ensure EG-Dev user has sufficient privileges
3. **Script Errors**: Check for syntax errors or missing dependencies

### Error Resolution
- If Part 1 fails: Check server connectivity and permissions
- If Part 2 fails: Ensure Part 1 completed successfully
- For foreign key errors: Verify table creation order

### Verification Commands
```sql
-- Check if database exists
SELECT name FROM sys.databases WHERE name = 'GoldRushThunee';

-- Check table structure
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES ORDER BY TABLE_NAME;

-- Check foreign keys
SELECT * FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS;
```

## Next Steps After Recreation
1. **Test Database Connectivity** from your application
2. **Update Connection Strings** in your application configuration
3. **Start ThuneeAPI** application and verify it connects
4. **Create Sample Data** if needed for testing
5. **Run Application Tests** to ensure everything works

## Support
If you encounter issues:
1. Check the SQL Server error logs
2. Verify all script files are present and complete
3. Ensure proper permissions on the database server
4. Contact your database administrator if needed

## File Locations
All scripts are located in the `Database_Setup_Scripts` folder:
- Main recreation scripts
- PowerShell automation
- Documentation and guides
- Legacy scripts (for reference)

---
**Last Updated**: 2025-08-11
**Database Version**: GoldRushThunee v1.0
