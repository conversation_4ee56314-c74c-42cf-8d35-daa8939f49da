-- =============================================
-- COMPLETE GoldRushThunee Database Recreation Script - PART 2
-- =============================================
-- This is the continuation of the main recreation script
-- Run this AFTER running COMPLETE_DATABASE_RECREATION_SCRIPT.sql
-- =============================================

USE GoldRushThunee;
GO

PRINT 'CONTINUING WITH STORED PROCEDURES - PART 2...';

-- =============================================
-- GAME MANAGEMENT PROCEDURES
-- =============================================

-- Create Game
CREATE PROCEDURE SP_CreateGame
    @Id UNIQUEIDENTIFIER,
    @LobbyCode NVARCHAR(6),
    @Team1Name NVARCHAR(50),
    @Team2Name NVARCHAR(50),
    @Team1Player1Id UNIQUEIDENTIFIER,
    @Team1Player2Id UNIQUEIDENTIFIER,
    @Team2Player1Id UNIQUEIDENTIFIER,
    @Team2Player2Id UNIQUEIDENTIFIER,
    @CompetitionId UNIQUEIDENTIFIER = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO Games (Id, LobbyCode, Team1Name, Team2Name, Team1Player1Id, Team1Player2Id, 
                          Team2Player1Id, Team2Player2Id, CompetitionId, Status, CreatedAt, UpdatedAt)
        VALUES (@Id, @LobbyCode, @Team1Name, @Team2Name, @Team1Player1Id, @Team1Player2Id,
                @Team2Player1Id, @Team2Player2Id, @CompetitionId, 'waiting', GETUTCDATE(), GETUTCDATE());
        
        SELECT * FROM Games WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Get Game by ID
CREATE PROCEDURE SP_GetGameById
    @Id UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Games WHERE Id = @Id;
END
GO

-- Get Game by Lobby Code
CREATE PROCEDURE SP_GetGameByLobbyCode
    @LobbyCode NVARCHAR(6)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT * FROM Games WHERE LobbyCode = @LobbyCode;
END
GO

-- Update Game Status
CREATE PROCEDURE SP_UpdateGameStatus
    @Id UNIQUEIDENTIFIER,
    @Status NVARCHAR(20)
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE Games 
    SET Status = @Status, UpdatedAt = GETUTCDATE()
    WHERE Id = @Id;
    
    SELECT * FROM Games WHERE Id = @Id;
END
GO

-- Complete Game
CREATE PROCEDURE SP_CompleteGame
    @Id UNIQUEIDENTIFIER,
    @WinnerTeam INT,
    @Team1Score INT,
    @Team2Score INT,
    @Team1BallsWon INT,
    @Team2BallsWon INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        UPDATE Games 
        SET Status = 'completed',
            WinnerTeam = @WinnerTeam,
            Team1Score = @Team1Score,
            Team2Score = @Team2Score,
            Team1BallsWon = @Team1BallsWon,
            Team2BallsWon = @Team2BallsWon,
            CompletedAt = GETUTCDATE(),
            UpdatedAt = GETUTCDATE()
        WHERE Id = @Id;
        
        SELECT * FROM Games WHERE Id = @Id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

PRINT '✓ Game management procedures created.';

-- =============================================
-- LEADERBOARD PROCEDURES
-- =============================================

-- Get Global Leaderboard
CREATE PROCEDURE SP_GetGlobalLeaderboard
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    SELECT 
        ROW_NUMBER() OVER (ORDER BY us.TotalScore DESC, us.WinRate DESC, us.GamesPlayed DESC) AS Rank,
        us.UserId AS PlayerId,
        us.Username AS PlayerName,
        us.TotalScore AS Score,
        us.GamesPlayed,
        us.GamesWon,
        us.GamesLost,
        ROUND(us.WinRate, 2) AS WinRate,
        us.HandsPlayed,
        us.CompetitionsJoined,
        us.CompetitionsWon
    FROM UserStatistics us
    WHERE us.GamesPlayed > 0
    ORDER BY us.TotalScore DESC, us.WinRate DESC, us.GamesPlayed DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- Get Competition Leaderboard
CREATE PROCEDURE SP_GetCompetitionLeaderboard
    @CompetitionId UNIQUEIDENTIFIER,
    @PageSize INT = 20,
    @PageNumber INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    SELECT 
        cl.Rank,
        cl.TeamId,
        cl.TeamName,
        cl.Player1Name,
        cl.Player2Name,
        cl.GamesPlayed,
        cl.Points,
        cl.BonusPoints,
        cl.TotalPoints,
        cl.MaxGames,
        cl.Status,
        CASE 
            WHEN cl.GamesPlayed = 0 THEN 0.0
            ELSE ROUND((CAST(cl.Points AS FLOAT) / CAST(cl.GamesPlayed AS FLOAT)) * 100, 2)
        END AS WinRate
    FROM CompetitionLeaderboard cl
    WHERE cl.CompetitionId = @CompetitionId
    ORDER BY cl.Rank
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
GO

-- Record Hand Result
CREATE PROCEDURE SP_RecordHandResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @HandNumber INT,
    @WinnerPlayerId UNIQUEIDENTIFIER,
    @Points INT,
    @TrumpSuit NVARCHAR(10) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @HandId UNIQUEIDENTIFIER = NEWID();
        
        INSERT INTO GameHands (Id, GameId, BallNumber, HandNumber, WinnerPlayerId, Points, TrumpSuit, CompletedAt)
        VALUES (@HandId, @GameId, @BallNumber, @HandNumber, @WinnerPlayerId, @Points, @TrumpSuit, GETUTCDATE());
        
        SELECT @HandId AS HandId, 'Hand recorded successfully' AS Message;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Record Ball Result
CREATE PROCEDURE SP_RecordBallResult
    @GameId UNIQUEIDENTIFIER,
    @BallNumber INT,
    @Team1Score INT,
    @Team2Score INT,
    @WinnerTeam INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO GameBalls (Id, GameId, BallNumber, Team1Score, Team2Score, WinnerTeam, CompletedAt)
        VALUES (NEWID(), @GameId, @BallNumber, @Team1Score, @Team2Score, @WinnerTeam, GETUTCDATE());
        
        SELECT 'Ball result recorded successfully' AS Message;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

PRINT '✓ Leaderboard and game tracking procedures created.';

-- =============================================
-- TEAM MANAGEMENT PROCEDURES
-- =============================================

-- Join Competition Team
CREATE PROCEDURE SP_JoinCompetitionTeam
    @CompetitionId UNIQUEIDENTIFIER,
    @TeamName NVARCHAR(50),
    @Player1Id UNIQUEIDENTIFIER,
    @InviteCode NVARCHAR(10)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @TeamId UNIQUEIDENTIFIER = NEWID();
        
        INSERT INTO CompetitionTeams (Id, CompetitionId, TeamName, Player1Id, InviteCode, RegisteredAt)
        VALUES (@TeamId, @CompetitionId, @TeamName, @Player1Id, @InviteCode, GETUTCDATE());
        
        SELECT * FROM CompetitionTeams WHERE Id = @TeamId;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- Accept Team Invite
CREATE PROCEDURE SP_AcceptTeamInvite
    @InviteCode NVARCHAR(10),
    @PlayerId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @TeamId UNIQUEIDENTIFIER;
        
        -- Get team ID from invite
        SELECT @TeamId = TeamId FROM CompetitionTeamInvites 
        WHERE InviteCode = @InviteCode AND Status = 'pending';
        
        IF @TeamId IS NOT NULL
        BEGIN
            -- Update team with second player
            UPDATE CompetitionTeams 
            SET Player2Id = @PlayerId, IsComplete = 1
            WHERE Id = @TeamId;
            
            -- Update invite status
            UPDATE CompetitionTeamInvites 
            SET Status = 'accepted', AcceptedAt = GETUTCDATE(), InviteeId = @PlayerId
            WHERE InviteCode = @InviteCode;
            
            SELECT * FROM CompetitionTeams WHERE Id = @TeamId;
        END
        ELSE
        BEGIN
            THROW 50001, 'Invalid or expired invite code', 1;
        END
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

PRINT '✓ Team management procedures created.';

PRINT '==============================================';
PRINT 'STORED PROCEDURES CREATION COMPLETE!';
PRINT '==============================================';
