import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TeamFixture } from '@/services/api';
import { Calendar, Clock, Trophy, Users, Play } from 'lucide-react';
import { format } from 'date-fns';

interface CompetitionFixturesProps {
  fixtures: TeamFixture[];
  onJoinMatch: (lobbyCode: string) => void;
}

export default function CompetitionFixtures({ fixtures, onJoinMatch }: CompetitionFixturesProps) {
  if (fixtures.length === 0) {
    return (
      <Card className="bg-black/50 border-[#E1C760]/30">
        <CardContent className="p-6 text-center">
          <Trophy className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-400">No fixtures available at the moment.</p>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'inprogress':
        return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed':
        return 'bg-green-500/20 text-green-400 border-green-500/30';
      default:
        return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMM d, yyyy 'at' h:mm a");
  };

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold text-[#E1C760] mb-4">Your Fixtures</h3>
      
      {fixtures.map((fixture) => (
        <Card key={fixture.lobbyId} className="bg-black/50 border-[#E1C760]/30">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-[#E1C760] text-lg">
                {fixture.phase} - Match {fixture.lobbyCode}
              </CardTitle>
              <Badge className={getStatusColor(fixture.matchStatus)}>
                {fixture.matchStatus}
              </Badge>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Match Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center text-gray-300">
                  <Calendar className="h-4 w-4 mr-2" />
                  <span className="text-sm">
                    {fixture.matchScheduledAt 
                      ? formatDate(fixture.matchScheduledAt)
                      : 'Schedule TBD'
                    }
                  </span>
                </div>
                
                <div className="flex items-center text-gray-300">
                  <Trophy className="h-4 w-4 mr-2" />
                  <span className="text-sm">
                    Best of {fixture.bestOfGames} (First to {fixture.requiredWins})
                  </span>
                </div>
              </div>
              
              {/* Opponent Info */}
              {fixture.opponent && (
                <div className="space-y-2">
                  <div className="flex items-center text-gray-300">
                    <Users className="h-4 w-4 mr-2" />
                    <span className="text-sm font-medium">vs {fixture.opponent.teamName}</span>
                  </div>
                  <div className="text-xs text-gray-400 ml-6">
                    {fixture.opponent.player1.username}
                    {fixture.opponent.player2.username && ` & ${fixture.opponent.player2.username}`}
                  </div>
                </div>
              )}
            </div>

            {/* Result Display */}
            {fixture.result && fixture.matchStatus === 'Completed' && (
              <div className="mt-4 p-3 rounded-lg bg-gray-800/50 border border-gray-600">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-300">Result:</span>
                  <div className={`text-sm font-medium ${
                    fixture.result.isWinner ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {fixture.result.isWinner ? 'Victory' : 'Defeat'}
                  </div>
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  Games Won: {fixture.result.gamesWon} | Games Lost: {fixture.result.gamesLost}
                </div>
              </div>
            )}

            {/* Action Button */}
            {fixture.canJoin && fixture.matchStatus !== 'Completed' && (
              <div className="mt-4">
                <Button
                  onClick={() => onJoinMatch(fixture.lobbyCode)}
                  className="w-full bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
                >
                  <Play className="h-4 w-4 mr-2" />
                  {fixture.matchStatus === 'InProgress' ? 'Rejoin Match' : 'Join Match'}
                </Button>
              </div>
            )}

            {/* Completed At */}
            {fixture.completedAt && (
              <div className="text-xs text-gray-500 mt-2">
                Completed: {formatDate(fixture.completedAt)}
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
