<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ThuneeAPI</name>
    </assembly>
    <members>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetAllUsers">
            <summary>
            Get all users (Accessible to all users)
            </summary>
            <returns>List of all users</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetUserById(System.Guid)">
            <summary>
            Get user by ID (Accessible to all users)
            </summary>
            <param name="userId">User ID</param>
            <returns>User details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.UpdateUser(System.Guid,ThuneeAPI.Application.DTOs.AdminUpdateUserDto)">
            <summary>
            Update user (Accessible to all users)
            </summary>
            <param name="userId">User ID</param>
            <param name="updateDto">Update data</param>
            <returns>Updated user</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.DeleteUser(System.Guid)">
            <summary>
            Delete user (Accessible to all users)
            </summary>
            <param name="userId">User ID</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.ChangeUserPassword(System.Guid,ThuneeAPI.Application.DTOs.ChangeUserPasswordDto)">
            <summary>
            Change user password (Accessible to all users)
            </summary>
            <param name="userId">User ID</param>
            <param name="passwordDto">New password data</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetAllCompetitions">
            <summary>
            Get all competitions (Accessible to all users)
            </summary>
            <returns>List of all competitions</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.CreateCompetition(ThuneeAPI.Application.DTOs.CreateCompetitionDto)">
            <summary>
            Create competition (Accessible to all users)
            </summary>
            <param name="createDto">Competition creation data</param>
            <returns>Created competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetCompetitionById(System.Guid)">
            <summary>
            Get competition by ID (Accessible to all users)
            </summary>
            <param name="competitionId">Competition ID</param>
            <returns>Competition details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.UpdateCompetition(System.Guid,ThuneeAPI.Application.DTOs.UpdateCompetitionDto)">
            <summary>
            Update competition (Accessible to all users)
            </summary>
            <param name="competitionId">Competition ID</param>
            <param name="updateDto">Update data</param>
            <returns>Updated competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.DeleteCompetition(System.Guid)">
            <summary>
            Delete competition (Accessible to all users)
            </summary>
            <param name="competitionId">Competition ID</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetCompetitionTeams(System.Guid)">
            <summary>
            Get competition teams (Accessible to all users)
            </summary>
            <param name="competitionId">Competition ID</param>
            <returns>List of teams in competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.DeleteCompetitionTeam(System.Guid)">
            <summary>
            Delete competition team (Accessible to all users)
            </summary>
            <param name="teamId">Team ID</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetCompetitionGames(System.Guid)">
            <summary>
            Get competition games (Accessible to all users)
            </summary>
            <param name="competitionId">Competition ID</param>
            <returns>List of games in competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.GetAllGames">
            <summary>
            Get all games (Accessible to all users)
            </summary>
            <returns>List of all games</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AdminController.SendCompetitionEmail(System.Guid,ThuneeAPI.Application.DTOs.CompetitionEmailDto)">
            <summary>
            Send email to all players in competition (Accessible to all users)
            </summary>
            <param name="competitionId">Competition ID</param>
            <param name="emailDto">Email data</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.Register(ThuneeAPI.Application.DTOs.RegisterUserDto)">
            <summary>
            Register a new user
            </summary>
            <param name="registerDto">User registration details</param>
            <returns>Authentication response with user details and token</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.ValidateToken">
            <summary>
            Validate JWT token
            </summary>
            <returns>Token validation result</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.GetCurrentUser">
            <summary>
            Get current user information
            </summary>
            <returns>Current user details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.Login(ThuneeAPI.Application.DTOs.LoginUserDto)">
            <summary>
            Login user
            </summary>
            <param name="loginDto">User login credentials</param>
            <returns>Authentication response with user details and token</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.VerifyOtp(ThuneeAPI.Application.DTOs.VerifyOtpDto)">
            <summary>
            Verify OTP (Development: use "1234" as the correct OTP)
            </summary>
            <param name="verifyOtpDto">OTP verification details</param>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.AuthController.Logout">
            <summary>
            Logout user
            </summary>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.AdvancePhase(System.Guid,ThuneeAPI.Controllers.AdvancePhaseDto)">
            <summary>
            Advance competition to next phase (Admin only)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetPhaseTeams(System.Guid,System.String)">
            <summary>
            Get teams for specific phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetEligibleTeams(System.Guid)">
            <summary>
            Get eligible teams for next phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.ProcessPhaseEnd(System.Guid)">
            <summary>
            Process phase end and advance teams (Admin only)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.CreatePhaseLobby(System.Guid,System.String,ThuneeAPI.Controllers.CreatePhaseLobbyDto)">
            <summary>
            Create phase lobby for knockout rounds (Admin only)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetPhaseLobbies(System.Guid,System.String)">
            <summary>
            Get phase lobbies
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetPhaseBracket(System.Guid,System.String)">
            <summary>
            Generate bracket for phase
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetCompetitionBrackets(System.Guid)">
            <summary>
            Get all competition brackets
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.SetLobbyWinner(System.Guid,System.Guid,ThuneeAPI.Controllers.SetLobbyWinnerDto)">
            <summary>
            Set lobby winner (Admin only)
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionPhaseController.GetPhaseRankings(System.Guid,System.String)">
            <summary>
            Get phase rankings
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitions">
            <summary>
            Get all competitions
            </summary>
            <returns>List of competitions</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetition(System.Guid)">
            <summary>
            Get competition by ID
            </summary>
            <param name="id">Competition ID</param>
            <returns>Competition details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.CreateCompetition(ThuneeAPI.Application.DTOs.CreateCompetitionDto)">
            <summary>
            Create a new competition (Admin only)
            </summary>
            <param name="createDto">Competition creation details</param>
            <returns>Created competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.CreateCompetitionTeam(System.Guid,ThuneeAPI.Application.DTOs.CreateCompetitionTeamDto)">
            <summary>
            Create a team for a competition
            </summary>
            <param name="id">Competition ID</param>
            <param name="createDto">Team creation details</param>
            <returns>Team details with invite code</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.JoinCompetitionTeam(ThuneeAPI.Application.DTOs.JoinCompetitionTeamDto)">
            <summary>
            Join a team using invite code
            </summary>
            <param name="joinDto">Join team details</param>
            <returns>Team details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionStatus(System.Guid)">
            <summary>
            Get competition status for current user
            </summary>
            <param name="id">Competition ID</param>
            <returns>Competition status</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.JoinCompetition(System.Guid,ThuneeAPI.Application.DTOs.JoinCompetitionDto)">
            <summary>
            Join a competition (legacy endpoint)
            </summary>
            <param name="id">Competition ID</param>
            <param name="joinDto">Join competition details</param>
            <returns>Team registration details</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionTeams(System.Guid)">
            <summary>
            Get competition teams
            </summary>
            <param name="id">Competition ID</param>
            <returns>List of registered teams</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.GetCompetitionLeaderboard(System.Guid,System.Int32,System.Int32)">
            <summary>
            Get competition leaderboard
            </summary>
            <param name="id">Competition ID</param>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <returns>Competition leaderboard</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.UpdateCompetition(System.Guid,ThuneeAPI.Application.DTOs.UpdateCompetitionDto)">
            <summary>
            Update competition (Admin only)
            </summary>
            <param name="id">Competition ID</param>
            <param name="updateDto">Update details</param>
            <returns>Updated competition</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.ProcessGameResult(ThuneeAPI.Application.DTOs.CompetitionGameResultDto)">
            <summary>
            Process game result for competition scoring
            </summary>
            <param name="gameResult">Game result details</param>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.CompetitionsController.DeleteCompetition(System.Guid)">
            <summary>
            Delete competition (Admin only)
            </summary>
            <param name="id">Competition ID</param>
            <returns>Success message</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.EmailTestController.SendTestEmail(System.String)">
            <summary>
            Test email sending functionality (Development only)
            </summary>
            <param name="email">Email address to send test email to</param>
            <returns>Test result</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.EmailTestController.SendWelcomeEmail(System.String,System.String)">
            <summary>
            Test welcome email template
            </summary>
            <param name="email">Email address to send welcome email to</param>
            <param name="username">Username for the welcome email</param>
            <returns>Test result</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.EmailTestController.SendTeamPairingEmail(System.String,System.String,System.String,System.String)">
            <summary>
            Test team pairing email template
            </summary>
            <param name="email">Email address to send team pairing email to</param>
            <param name="username">Username for the email</param>
            <param name="teamName">Team name</param>
            <param name="partnerName">Partner's name</param>
            <returns>Test result</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.EmailTestController.SendKnockoutLobbyEmail(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Test knockout lobby email template
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.EmailTestController.SendPhaseAdvancementEmail(System.String,System.String,System.String,System.String,System.String,System.Boolean)">
            <summary>
            Test phase advancement email template
            </summary>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.CreateGame(ThuneeAPI.Application.DTOs.CreateGameDto)">
            <summary>
            Create a new game lobby
            </summary>
            <param name="createGameDto">Game creation details</param>
            <returns>Created game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetGame(System.String)">
            <summary>
            Get game information by lobby code
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.JoinGame(System.String,ThuneeAPI.Application.DTOs.JoinGameDto)">
            <summary>
            Join an existing game
            </summary>
            <param name="lobbyCode">The lobby code to join</param>
            <param name="joinGameDto">Join game details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.StartGame(System.String)">
            <summary>
            Start a game (host only)
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordHandResult(System.String,ThuneeAPI.Application.DTOs.RecordHandResultDto)">
            <summary>
            Record the result of a hand
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="handResultDto">Hand result details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordBallResult(System.String,ThuneeAPI.Application.DTOs.RecordBallResultDto)">
            <summary>
            Record the result of a ball
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="ballResultDto">Ball result details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.RecordGameResult(System.String,ThuneeAPI.Application.DTOs.GameResultDto)">
            <summary>
            Record the final game result
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="gameResultDto">Game result details</param>
            <returns>Updated game information</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetUserGames(System.Int32,System.Int32)">
            <summary>
            Get user's game history
            </summary>
            <param name="page">Page number</param>
            <param name="pageSize">Items per page</param>
            <returns>List of user's games</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetGameHands(System.String)">
            <summary>
            Get detailed hand history for a game
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>List of game hands</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetBallScores(System.String)">
            <summary>
            Get current ball scores for a game
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Current ball scores</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.GetActiveGames">
            <summary>
            Get active games/lobbies
            </summary>
            <returns>List of active games</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.LeaveGame(System.String)">
            <summary>
            Leave a game
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GamesController.SetPlayerReady(System.String,ThuneeAPI.Application.DTOs.SetPlayerReadyDto)">
            <summary>
            Set player ready status
            </summary>
            <param name="lobbyCode">The lobby code</param>
            <param name="readyDto">Ready status</param>
            <returns>Success response</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GameSettingsController.GetGameSettings">
            <summary>
            Get current game settings
            </summary>
            <returns>Current game settings</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GameSettingsController.UpdateGameSettings(ThuneeAPI.Application.DTOs.UpdateGameSettingsDto)">
            <summary>
            Update game settings
            </summary>
            <param name="updateDto">Settings to update</param>
            <returns>Updated game settings</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.GameSettingsController.ResetGameSettings">
            <summary>
            Reset game settings to defaults
            </summary>
            <returns>Reset game settings</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.LeaderboardController.GetGlobalLeaderboard(System.String,System.String,System.Int32,System.Int32)">
            <summary>
            Get global leaderboard
            </summary>
            <param name="timeFrame">Time frame filter (all, weekly, monthly)</param>
            <param name="sortBy">Sort criteria (score, winRate, gamesPlayed)</param>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Global leaderboard</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.LeaderboardController.GetWeeklyLeaderboard(System.Int32,System.Int32)">
            <summary>
            Get weekly leaderboard
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Weekly leaderboard</returns>
        </member>
        <member name="M:ThuneeAPI.Controllers.LeaderboardController.GetMonthlyLeaderboard(System.Int32,System.Int32)">
            <summary>
            Get monthly leaderboard
            </summary>
            <param name="page">Page number</param>
            <param name="limit">Items per page</param>
            <returns>Monthly leaderboard</returns>
        </member>
    </members>
</doc>
